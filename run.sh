#!/bin/bash

# Tool Nổ Hũ PG - Linux/Mac Runner
clear

echo "================================================================"
echo "                    🎰 TOOL NỔ HŨ PG 🎰"
echo "================================================================"
echo ""
echo "🚀 Đang khởi động server..."
echo ""

# Kiểm tra Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Lỗi: Python chưa được cài đặt!"
        echo ""
        echo "💡 Vui lòng cài đặt Python:"
        echo "   Ubuntu/Debian: sudo apt install python3"
        echo "   CentOS/RHEL: sudo yum install python3"
        echo "   macOS: brew install python3"
        echo ""
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ Python đã sẵn sàng ($PYTHON_CMD)"
echo "🌐 Đang khởi động HTTP Server..."
echo ""

# Chạy server
$PYTHON_CMD server.py

echo ""
echo "👋 Server đã dừng!"
