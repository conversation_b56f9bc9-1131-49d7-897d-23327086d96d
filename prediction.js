// Prediction Tool for PG Slots
class ScatterPredictor {
    constructor() {
        this.isRunning = false;
        this.selectedGame = null;
        this.spinHistory = [];
        this.predictionHistory = [];
        this.stats = {
            totalSpins: 0,
            scatterHits: 0,
            accuracy: 0,
            dryStreak: 0,
            currentStreak: 0
        };
        this.chart = null;
        this.predictionInterval = null;
        
        this.init();
    }
    
    init() {
        this.loadGames();
        this.setupEventListeners();
        this.initChart();
        this.loadSavedData();
    }
    
    loadGames() {
        const gameSelect = document.getElementById('gameSelect');
        const games = [
            { id: 1, name: "Kho Báu Aztec" },
            { id: 2, name: "Đường Mật Chúa" },
            { id: 3, name: "Đường Mật Chúa 2" },
            { id: 4, name: "Neko May Mắn" },
            { id: 5, name: "<PERSON><PERSON><PERSON>" },
            { id: 6, name: "Wild Dao Tặc" },
            { id: 7, name: "Pháo Ho<PERSON> Wild" },
            { id: 8, name: "<PERSON><PERSON><PERSON>" }
        ];
        
        games.forEach(game => {
            const option = document.createElement('option');
            option.value = game.id;
            option.textContent = game.name;
            gameSelect.appendChild(option);
        });
    }
    
    setupEventListeners() {
        const gameSelect = document.getElementById('gameSelect');
        gameSelect.addEventListener('change', (e) => {
            if (e.target.value) {
                this.selectGame(e.target.value, e.target.options[e.target.selectedIndex].text);
            }
        });
    }
    
    selectGame(gameId, gameName) {
        this.selectedGame = { id: gameId, name: gameName };
        document.getElementById('currentGame').textContent = gameName;
        document.getElementById('predictionDashboard').style.display = 'grid';
        this.updateDisplay();
    }
    
    initChart() {
        const ctx = document.getElementById('predictionChart').getContext('2d');
        this.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Xác suất Scatter (%)',
                    data: [],
                    borderColor: '#ff6600',
                    backgroundColor: 'rgba(255, 102, 0, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#ff6600'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: '#ffcc66' },
                        grid: { color: 'rgba(255, 102, 0, 0.2)' }
                    },
                    y: {
                        ticks: { color: '#ffcc66' },
                        grid: { color: 'rgba(255, 102, 0, 0.2)' },
                        min: 0,
                        max: 100
                    }
                }
            }
        });
    }
    
    startPrediction() {
        if (!this.selectedGame) {
            this.showNotification('Vui lòng chọn game trước!', 'error');
            return;
        }
        
        this.isRunning = true;
        document.querySelector('.btn-start').disabled = true;
        document.querySelector('.btn-stop').disabled = false;
        
        this.predictionInterval = setInterval(() => {
            this.runPrediction();
        }, 2000);
        
        this.showNotification('Đã bắt đầu dự đoán!', 'success');
    }
    
    stopPrediction() {
        this.isRunning = false;
        document.querySelector('.btn-start').disabled = false;
        document.querySelector('.btn-stop').disabled = true;
        
        if (this.predictionInterval) {
            clearInterval(this.predictionInterval);
        }
        
        this.showNotification('Đã dừng dự đoán!', 'info');
    }
    
    runPrediction() {
        // Simulate advanced prediction algorithm
        const currentTime = new Date();
        const timePattern = this.analyzeTimePattern();
        const historyPattern = this.analyzeHistoryPattern();
        const streakFactor = this.calculateStreakFactor();
        
        // Complex prediction calculation
        let baseChance = 15; // Base 15% chance
        let timeBonus = timePattern * 10;
        let historyBonus = historyPattern * 15;
        let streakBonus = streakFactor * 20;
        
        // Add randomness to simulate real patterns
        let randomFactor = (Math.random() - 0.5) * 10;
        
        let finalChance = Math.max(0, Math.min(100, 
            baseChance + timeBonus + historyBonus + streakBonus + randomFactor
        ));
        
        this.updatePredictionMeter(finalChance);
        this.updateChart(finalChance);
        this.updatePredictionStatus(finalChance);
        
        // Simulate spin result
        this.simulateSpin(finalChance);
        
        this.saveData();
    }
    
    analyzeTimePattern() {
        const now = new Date();
        const hour = now.getHours();
        const minute = now.getMinutes();
        
        // Peak hours simulation (higher chance during certain times)
        if ((hour >= 20 && hour <= 23) || (hour >= 6 && hour <= 8)) {
            return 0.3;
        } else if (hour >= 12 && hour <= 14) {
            return 0.2;
        }
        return 0.1;
    }
    
    analyzeHistoryPattern() {
        if (this.spinHistory.length < 10) return 0.1;
        
        const recent = this.spinHistory.slice(-10);
        const scatterCount = recent.filter(spin => spin.hasScatter).length;
        
        // If few scatters recently, increase chance
        if (scatterCount <= 1) return 0.4;
        if (scatterCount <= 2) return 0.2;
        return 0.1;
    }
    
    calculateStreakFactor() {
        if (this.stats.currentStreak > 20) return 0.5;
        if (this.stats.currentStreak > 15) return 0.3;
        if (this.stats.currentStreak > 10) return 0.2;
        return 0.1;
    }
    
    simulateSpin(predictedChance) {
        // Simulate actual spin result
        const hasScatter = Math.random() * 100 < (predictedChance * 0.3 + 5); // Adjust for realism
        
        const spinResult = {
            time: new Date(),
            predictedChance: Math.round(predictedChance),
            hasScatter: hasScatter,
            correct: (predictedChance > 70 && hasScatter) || (predictedChance <= 70 && !hasScatter)
        };
        
        this.spinHistory.push(spinResult);
        this.predictionHistory.push(spinResult);
        
        // Update stats
        this.stats.totalSpins++;
        if (hasScatter) {
            this.stats.scatterHits++;
            this.stats.currentStreak = 0;
        } else {
            this.stats.currentStreak++;
        }
        
        this.stats.dryStreak = Math.max(this.stats.dryStreak, this.stats.currentStreak);
        
        // Calculate accuracy
        const correctPredictions = this.predictionHistory.filter(p => p.correct).length;
        this.stats.accuracy = Math.round((correctPredictions / this.predictionHistory.length) * 100);
        
        this.updateStats();
        this.updateHistory();
        
        // Auto spin if enabled and high chance
        if (document.getElementById('autoSpin').checked && predictedChance > 80) {
            this.showNotification('🎯 TÍN HIỆU MẠNH! Khuyến nghị quay ngay!', 'warning');
        }
    }
    
    updatePredictionMeter(percentage) {
        const meter = document.getElementById('predictionMeter');
        const percentageText = document.getElementById('predictionPercentage');
        
        meter.style.width = percentage + '%';
        percentageText.textContent = Math.round(percentage) + '%';
        
        // Add glow effect for high chances
        if (percentage > 75) {
            meter.classList.add('high-chance');
        } else {
            meter.classList.remove('high-chance');
        }
    }
    
    updateChart(percentage) {
        const now = new Date().toLocaleTimeString();
        
        this.chart.data.labels.push(now);
        this.chart.data.datasets[0].data.push(percentage);
        
        // Keep only last 20 data points
        if (this.chart.data.labels.length > 20) {
            this.chart.data.labels.shift();
            this.chart.data.datasets[0].data.shift();
        }
        
        this.chart.update('none');
    }
    
    updatePredictionStatus(percentage) {
        const status = document.getElementById('predictionStatus');
        
        if (percentage > 80) {
            status.textContent = '🔥 CƠ HỘI CAO! Khuyến nghị quay ngay!';
            status.style.background = 'linear-gradient(45deg, #ff0000, #ff6600)';
        } else if (percentage > 60) {
            status.textContent = '⚡ Cơ hội tốt, có thể thử quay';
            status.style.background = 'linear-gradient(45deg, #ff6600, #ffaa00)';
        } else if (percentage > 40) {
            status.textContent = '⏳ Cơ hội trung bình, chờ thêm';
            status.style.background = 'linear-gradient(45deg, #ffaa00, #ffcc00)';
        } else {
            status.textContent = '❄️ Cơ hội thấp, nên chờ';
            status.style.background = 'rgba(255, 102, 0, 0.1)';
        }
    }
    
    updateStats() {
        document.getElementById('totalSpins').textContent = this.stats.totalSpins;
        document.getElementById('scatterHits').textContent = this.stats.scatterHits;
        document.getElementById('accuracy').textContent = this.stats.accuracy + '%';
        document.getElementById('dryStreak').textContent = this.stats.currentStreak;
        
        const scatterRate = this.stats.totalSpins > 0 ? 
            Math.round((this.stats.scatterHits / this.stats.totalSpins) * 100) : 0;
        document.getElementById('scatterRate').textContent = scatterRate + '%';
        
        document.getElementById('lastSpin').textContent = 
            this.spinHistory.length > 0 ? 
            this.spinHistory[this.spinHistory.length - 1].time.toLocaleTimeString() : '-';
    }
    
    updateHistory() {
        const historyList = document.getElementById('historyList');
        historyList.innerHTML = '';
        
        const recent = this.predictionHistory.slice(-10).reverse();
        
        recent.forEach(item => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            
            const resultText = item.hasScatter ? '✅ Scatter' : '❌ Không';
            const correctText = item.correct ? '✅' : '❌';
            
            historyItem.innerHTML = `
                <span class="time">${item.time.toLocaleTimeString()}</span>
                <span class="prediction">${item.predictedChance}%</span>
                <span class="result">${resultText} ${correctText}</span>
            `;
            
            historyList.appendChild(historyItem);
        });
    }
    
    resetData() {
        if (confirm('Bạn có chắc muốn xóa tất cả dữ liệu?')) {
            this.spinHistory = [];
            this.predictionHistory = [];
            this.stats = {
                totalSpins: 0,
                scatterHits: 0,
                accuracy: 0,
                dryStreak: 0,
                currentStreak: 0
            };
            
            this.chart.data.labels = [];
            this.chart.data.datasets[0].data = [];
            this.chart.update();
            
            this.updateStats();
            this.updateHistory();
            this.saveData();
            
            this.showNotification('Đã reset tất cả dữ liệu!', 'success');
        }
    }
    
    updateDisplay() {
        this.updateStats();
        this.updateHistory();
    }
    
    saveData() {
        const data = {
            spinHistory: this.spinHistory,
            predictionHistory: this.predictionHistory,
            stats: this.stats,
            selectedGame: this.selectedGame
        };
        localStorage.setItem('scatterPredictorData', JSON.stringify(data));
    }
    
    loadSavedData() {
        const saved = localStorage.getItem('scatterPredictorData');
        if (saved) {
            const data = JSON.parse(saved);
            this.spinHistory = data.spinHistory || [];
            this.predictionHistory = data.predictionHistory || [];
            this.stats = data.stats || this.stats;
            
            if (data.selectedGame) {
                this.selectedGame = data.selectedGame;
                document.getElementById('gameSelect').value = data.selectedGame.id;
                document.getElementById('currentGame').textContent = data.selectedGame.name;
                document.getElementById('predictionDashboard').style.display = 'grid';
            }
            
            this.updateDisplay();
        }
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        const colors = {
            success: '#00ff00',
            error: '#ff0000',
            warning: '#ffaa00',
            info: '#00aaff'
        };
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${colors[type]};
            color: #000;
            padding: 15px 25px;
            border-radius: 10px;
            font-weight: 700;
            z-index: 1000;
            animation: slideIn 0.3s ease;
            max-width: 300px;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 4000);
    }
}

// Global functions
function startPrediction() {
    predictor.startPrediction();
}

function stopPrediction() {
    predictor.stopPrediction();
}

function resetData() {
    predictor.resetData();
}

// Initialize when page loads
let predictor;
document.addEventListener('DOMContentLoaded', function() {
    predictor = new ScatterPredictor();
});
