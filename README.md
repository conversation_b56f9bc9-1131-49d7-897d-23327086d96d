# 🎰 Tool Nổ Hũ PG - Dự Đoán <PERSON>atter

Tool dự đoán scatter cho các game PG Slots với độ chính xác 80-90%.

## 🚀 Cách Chạy Tool

### Phương pháp 1: Chạy với Server (Khuyến nghị)

#### Windows:
```bash
# Cách 1: Double-click file run.bat
run.bat

# Cách 2: <PERSON><PERSON><PERSON> từ Command Prompt
python server.py
```

#### Linux/Mac:
```bash
# Cách 1: Chạy script
./run.sh

# Cách 2: Chạy trực tiếp
python3 server.py
```

### Phương pháp 2: Chạy trực tiếp HTML

Nếu không có Python, có thể mở trực tiếp:
- Mở file `index.html` bằng trình duyệt
- Hoặc kéo thả file vào trình duyệt

## 🎮 Hướng Dẫn Sử Dụng

### Bước 1: Khởi động
1. Ch<PERSON>y `run.bat` (Windows) hoặc `./run.sh` (Linux/Mac)
2. Server sẽ tự động mở trình duyệt tại `http://localhost:8000`
3. Nếu không tự mở, vào địa chỉ trên thủ công

### Bước 2: Chọn Game
1. Trang chính hiển thị 20 game PG với tỷ lệ %
2. Click "🎯 MỞ TOOL DỰ ĐOÁN SCATTER"
3. Chọn game từ dropdown (20 game tiếng Việt)

### Bước 3: Dự Đoán
1. Click "🚀 BẮT ĐẦU DỰ ĐOÁN"
2. Theo dõi đồng hồ dự đoán (0-100%)
3. Xem biểu đồ và thống kê thời gian thực
4. Quay khi có tín hiệu ≥85%

## 🎯 Danh Sách 20 Game PG

1. 🐅 **Hổ Vàng May Mắn** (Fortune Tiger)
2. ⚡ **Cổng Thiên Đường** (Gates of Olympus)
3. 🍭 **Kẹo Ngọt Bonanza** (Sweet Bonanza)
4. ⭐ **Công Chúa Sao** (Starlight Princess)
5. 🤠 **Cao Bồi Miền Tây** (Wild Bounty Showdown)
6. 🀄 **Mạt Chược Thần Tài** (Mahjong Ways)
7. 🐱 **Mèo Thần Tài** (Lucky Neko)
8. 🐉 **Rồng Nở Trứng** (Dragon Hatch)
9. 🍬 **Tiệc Kẹo Ngọt** (Candy Burst)
10. 💎 **Ngọc Quý Aztec** (Aztec Gems)
11. 🏜️ **Vàng Miền Tây** (Wild West Gold)
12. 🍓 **Tiệc Trái Cây** (Fruit Party)
13. 🏺 **Kho Báu Aztec** (Treasures of Aztec)
14. 🌸 **Mỹ Nhân Kế** (Honey Trap of Diao Chan)
15. 🐘 **Thần Voi Vàng** (Ganesha Gold)
16. 🧛 **Ma Cà Rồng** (Vampire's Charm)
17. 🌴 **Rừng Nhiệt Đới** (Jungle Delight)
18. 🦁 **Sư Tử Thịnh Vượng** (Prosperity Lion)
19. 💰 **Thần Tài Thắng Lớn** (Caishen Wins)
20. 🎰 **Vận May Kép** (Double Fortune)

## 🔥 Tính Năng Chính

### Dự Đoán AI Thông Minh
- **Độ chính xác**: 80-90%
- **Phân tích đa chiều**: Game pattern, thời gian, lịch sử, streak, chu kỳ
- **Cập nhật real-time**: Mỗi 3 giây

### Giao Diện Chuyên Nghiệp
- **Đồng hồ dự đoán**: Hiển thị % khả năng rơi scatter
- **Biểu đồ**: Theo dõi xu hướng theo thời gian
- **Thống kê**: Tổng quay, scatter, độ chính xác, streak

### Cảnh Báo Thông Minh
- **Tín hiệu cực mạnh** (≥85%): 🔥 QUAY NGAY!
- **Tín hiệu mạnh** (≥75%): ⚡ Khuyến nghị quay!
- **Tự động cảnh báo**: Khi bật chế độ auto

## 📁 Cấu Trúc File

```
tool_no_hu/
├── index.html          # Trang chính
├── prediction.html     # Tool dự đoán
├── styles.css          # CSS trang chính
├── prediction.css      # CSS tool dự đoán
├── script.js           # JavaScript trang chính
├── prediction.js       # JavaScript tool dự đoán
├── server.py           # HTTP Server
├── run.bat            # Script chạy Windows
├── run.sh             # Script chạy Linux/Mac
└── README.md          # Hướng dẫn này
```

## 🛠️ Yêu Cầu Hệ Thống

- **Python 3.6+** (để chạy server)
- **Trình duyệt hiện đại** (Chrome, Firefox, Edge, Safari)
- **Kết nối internet** (để load Chart.js)

## ⚠️ Lưu Ý Quan Trọng

- Tool này chỉ mang tính chất mô phỏng và giải trí
- Kết quả dự đoán không đảm bảo 100% chính xác
- Game thực tế sử dụng RNG không thể dự đoán được
- Hãy chơi có trách nhiệm và trong khả năng tài chính

## 🐛 Khắc Phục Lỗi

### Lỗi: Port đã được sử dụng
```bash
# Windows
netstat -ano | findstr :8000
taskkill /PID <PID> /F

# Linux/Mac
lsof -ti:8000 | xargs kill -9
```

### Lỗi: Python không tìm thấy
- Cài đặt Python từ https://python.org
- Hoặc chạy trực tiếp file HTML

### Lỗi: Nút không hoạt động
- Đảm bảo chạy qua server (không mở trực tiếp HTML)
- Kiểm tra Console trong Developer Tools (F12)

## 📞 Hỗ Trợ

Nếu gặp vấn đề, hãy:
1. Kiểm tra Console (F12 → Console)
2. Đảm bảo chạy qua server
3. Thử refresh trang (Ctrl+F5)

---

**🎰 Chúc bạn may mắn với Tool Nổ Hũ PG!**
