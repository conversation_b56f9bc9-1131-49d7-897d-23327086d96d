* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', monospace;
    background: linear-gradient(135deg, #0a0a2e 0%, #16213e 50%, #0f3460 100%);
    color: #ff6600;
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 2px solid #ff6600;
    margin-bottom: 30px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo img {
    width: 50px;
    height: 50px;
    filter: drop-shadow(0 0 10px #ff6600);
}

.logo-text .hack {
    font-size: 28px;
    font-weight: 900;
    color: #ff6600;
    text-shadow: 0 0 10px #ff6600;
}

.logo-text .slot {
    font-size: 28px;
    font-weight: 900;
    color: #ffaa00;
    text-shadow: 0 0 10px #ffaa00;
}

.logo-text .subtitle {
    font-size: 12px;
    color: #ffcc66;
    margin-top: -5px;
}

.user-info {
    display: flex;
    gap: 20px;
    align-items: center;
}

.username, .balance {
    padding: 8px 16px;
    background: rgba(255, 102, 0, 0.1);
    border: 1px solid #ff6600;
    border-radius: 20px;
    font-weight: 700;
    text-shadow: 0 0 5px #ff6600;
}

/* Back Button */
.btn-back {
    background: linear-gradient(45deg, #ff6600, #ffaa00);
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Orbitron', monospace;
    margin-bottom: 20px;
}

.btn-back:hover {
    transform: scale(1.05);
    box-shadow: 0 0 20px #ff6600;
}

/* Game Selection */
.game-selection {
    text-align: center;
    margin-bottom: 30px;
}

.game-selection h2 {
    font-size: 24px;
    margin-bottom: 20px;
    text-shadow: 0 0 10px #ff6600;
}

.game-select {
    width: 300px;
    padding: 12px 20px;
    background: rgba(255, 102, 0, 0.1);
    border: 2px solid #ff6600;
    border-radius: 25px;
    color: #ff6600;
    font-family: 'Orbitron', monospace;
    font-size: 14px;
    outline: none;
    cursor: pointer;
}

.game-select option {
    background: #0a0a2e;
    color: #ff6600;
}

/* Dashboard Grid */
.prediction-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

/* Card Styles */
.status-card, .prediction-card, .control-card, .stats-card, .chart-card, .history-card {
    background: linear-gradient(135deg, rgba(255, 102, 0, 0.1), rgba(255, 170, 0, 0.05));
    border: 2px solid #ff6600;
    border-radius: 15px;
    padding: 20px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.status-card:hover, .prediction-card:hover, .control-card:hover, 
.stats-card:hover, .chart-card:hover, .history-card:hover {
    box-shadow: 0 10px 30px rgba(255, 102, 0, 0.3);
    transform: translateY(-2px);
}

.status-card h3, .prediction-card h3, .control-card h3, 
.stats-card h3, .chart-card h3, .history-card h3 {
    font-size: 18px;
    margin-bottom: 15px;
    text-align: center;
    color: #ffaa00;
    text-shadow: 0 0 10px #ffaa00;
}

/* Status Panel */
.status-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 102, 0, 0.2);
}

.status-item .label {
    color: #ffcc66;
}

.status-item .value {
    color: #ff6600;
    font-weight: 700;
}

/* Prediction Meter */
.meter-container {
    position: relative;
    width: 100%;
    height: 80px;
    background: rgba(255, 102, 0, 0.1);
    border-radius: 40px;
    overflow: hidden;
    margin: 20px 0;
}

.meter-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff0000, #ff6600, #ffaa00, #00ff00);
    border-radius: 40px;
    transition: width 0.5s ease;
    width: 0%;
}

.meter-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #fff;
    font-weight: 900;
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.8);
}

.meter-label {
    display: block;
    font-size: 12px;
    margin-top: 5px;
}

.prediction-status {
    text-align: center;
    padding: 10px;
    background: rgba(255, 102, 0, 0.1);
    border-radius: 10px;
    font-weight: 700;
    color: #ffaa00;
}

/* Control Panel */
.control-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

.btn-control {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-start {
    background: linear-gradient(45deg, #00ff00, #66ff66);
    color: #000;
}

.btn-stop {
    background: linear-gradient(45deg, #ff0000, #ff6666);
    color: #fff;
}

.btn-reset {
    background: linear-gradient(45deg, #ffaa00, #ffcc66);
    color: #000;
}

.btn-control:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(255, 102, 0, 0.3);
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #ffcc66;
    cursor: pointer;
}

.checkbox-container input {
    width: 20px;
    height: 20px;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    text-align: center;
    padding: 10px;
    background: rgba(255, 102, 0, 0.1);
    border-radius: 10px;
}

.stat-label {
    font-size: 12px;
    color: #ffcc66;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 18px;
    font-weight: 900;
    color: #ff6600;
}

/* Chart Panel */
.chart-card {
    grid-column: 1 / -1;
}

#predictionChart {
    width: 100% !important;
    height: 300px !important;
}

/* History Panel */
.history-card {
    grid-column: 1 / -1;
}

.history-list {
    max-height: 200px;
    overflow-y: auto;
}

.history-item {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 10px;
    padding: 10px;
    border-bottom: 1px solid rgba(255, 102, 0, 0.2);
    font-size: 12px;
}

.history-item .time {
    color: #ffcc66;
}

.history-item .prediction {
    color: #ffaa00;
    font-weight: 700;
}

.history-item .result {
    color: #ff6600;
    font-weight: 700;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .prediction-dashboard {
        grid-template-columns: 1fr;
    }
    
    .game-select {
        width: 100%;
        max-width: 300px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* Animations */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.prediction-status.analyzing {
    animation: pulse 2s infinite;
}

@keyframes glow {
    0% { box-shadow: 0 0 5px #ff6600; }
    50% { box-shadow: 0 0 20px #ff6600; }
    100% { box-shadow: 0 0 5px #ff6600; }
}

.meter-fill.high-chance {
    animation: glow 1s infinite;
}
