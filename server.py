#!/usr/bin/env python3
"""
Simple HTTP Server để chạy Tool Nổ Hũ PG
Chạy lệnh: python server.py
Sau đó mở trình duyệt: http://localhost:8000
"""

import http.server
import socketserver
import webbrowser
import os
import sys

# Cấu hình
PORT = 8000
HOST = 'localhost'

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Thêm headers để tránh CORS issues
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

    def log_message(self, format, *args):
        # Custom log format
        print(f"[SERVER] {format % args}")

def main():
    # <PERSON>y<PERSON>n đến thư mục chứa tool
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print("=" * 60)
    print("🎰 TOOL NỔ HŨ PG - SERVER KHỞI ĐỘNG")
    print("=" * 60)
    print(f"📁 Thư mục: {script_dir}")
    print(f"🌐 Server: http://{HOST}:{PORT}")
    print(f"📄 Trang chính: http://{HOST}:{PORT}/index.html")
    print(f"🎯 Tool dự đoán: http://{HOST}:{PORT}/prediction.html")
    print("=" * 60)
    
    try:
        # Tạo server
        with socketserver.TCPServer((HOST, PORT), CustomHTTPRequestHandler) as httpd:
            print(f"✅ Server đang chạy tại http://{HOST}:{PORT}")
            print("📝 Nhấn Ctrl+C để dừng server")
            print("🚀 Đang mở trình duyệt...")
            
            # Tự động mở trình duyệt
            try:
                webbrowser.open(f'http://{HOST}:{PORT}/index.html')
            except Exception as e:
                print(f"⚠️  Không thể tự động mở trình duyệt: {e}")
                print(f"👉 Vui lòng mở thủ công: http://{HOST}:{PORT}/index.html")
            
            print("\n🎮 HƯỚNG DẪN SỬ DỤNG:")
            print("1. Trang chính sẽ hiển thị danh sách games")
            print("2. Click 'MỞ TOOL DỰ ĐOÁN SCATTER' để mở tool")
            print("3. Chọn game và bắt đầu dự đoán")
            print("4. Theo dõi tỷ lệ dự đoán 80-90%")
            print("\n" + "=" * 60)
            
            # Chạy server
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n" + "=" * 60)
        print("🛑 Server đã dừng!")
        print("👋 Cảm ơn bạn đã sử dụng Tool Nổ Hũ PG")
        print("=" * 60)
        
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Lỗi: Port {PORT} đã được sử dụng!")
            print("💡 Giải pháp:")
            print(f"   - Đóng ứng dụng đang sử dụng port {PORT}")
            print(f"   - Hoặc thay đổi PORT trong file server.py")
            print(f"   - Hoặc chạy: lsof -ti:{PORT} | xargs kill -9")
        else:
            print(f"❌ Lỗi server: {e}")
        sys.exit(1)
        
    except Exception as e:
        print(f"❌ Lỗi không xác định: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
