* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', monospace;
    background: linear-gradient(135deg, #0a1a0a 0%, #1a3a1a 50%, #0a2a0a 100%);
    color: #00ff66;
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 2px solid #00ff66;
    margin-bottom: 30px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo img {
    width: 50px;
    height: 50px;
    filter: drop-shadow(0 0 10px #00ff66);
}

.logo-text .hack {
    font-size: 28px;
    font-weight: 900;
    color: #ff6600;
    text-shadow: 0 0 10px #ff6600;
}

.logo-text .slot {
    font-size: 28px;
    font-weight: 900;
    color: #00ff66;
    text-shadow: 0 0 10px #00ff66;
}

.logo-text .subtitle {
    font-size: 12px;
    color: #66ff99;
    margin-top: -5px;
}

.user-info {
    display: flex;
    gap: 20px;
    align-items: center;
}

.username, .balance {
    padding: 8px 16px;
    background: rgba(0, 255, 102, 0.1);
    border: 1px solid #00ff66;
    border-radius: 20px;
    font-weight: 700;
    text-shadow: 0 0 5px #00ff66;
}

/* Main Content */
.back-button {
    margin-bottom: 20px;
}

.btn-back {
    background: linear-gradient(45deg, #00ff66, #66ff99);
    color: #000;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Orbitron', monospace;
}

.btn-back:hover {
    transform: scale(1.05);
    box-shadow: 0 0 20px #00ff66;
}

.section-title {
    text-align: center;
    font-size: 32px;
    font-weight: 900;
    margin-bottom: 30px;
    text-shadow: 0 0 20px #00ff66;
    background: linear-gradient(45deg, #00ff66, #66ff99);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.search-container {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
}

.search-input {
    width: 300px;
    padding: 12px 20px;
    background: rgba(0, 255, 102, 0.1);
    border: 2px solid #00ff66;
    border-radius: 25px;
    color: #00ff66;
    font-family: 'Orbitron', monospace;
    font-size: 14px;
    outline: none;
    transition: all 0.3s ease;
}

.search-input:focus {
    box-shadow: 0 0 20px rgba(0, 255, 102, 0.5);
    background: rgba(0, 255, 102, 0.2);
}

.search-input::placeholder {
    color: #66ff99;
}

/* Games Grid */
.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

.game-card {
    background: linear-gradient(135deg, rgba(0, 255, 102, 0.1), rgba(102, 255, 153, 0.05));
    border: 2px solid #00ff66;
    border-radius: 15px;
    padding: 20px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.game-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 255, 102, 0.3);
    border-color: #66ff99;
}

.game-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.game-card:hover::before {
    left: 100%;
}

.game-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.game-icon {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    border: 2px solid #00ff66;
    object-fit: cover;
}

.game-info h3 {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 5px;
    color: #00ff66;
}

.game-percentage {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 255, 102, 0.9);
    color: #000;
    padding: 5px 10px;
    border-radius: 50%;
    font-weight: 900;
    font-size: 14px;
    min-width: 50px;
    text-align: center;
}

.progress-container {
    margin-top: 15px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(0, 255, 102, 0.2);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00ff66, #66ff99);
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
    font-size: 12px;
    color: #66ff99;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .games-grid {
        grid-template-columns: 1fr;
    }
    
    .search-input {
        width: 100%;
        max-width: 300px;
    }
}

/* Animation for page load */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.game-card {
    animation: fadeInUp 0.6s ease forwards;
}

.game-card:nth-child(1) { animation-delay: 0.1s; }
.game-card:nth-child(2) { animation-delay: 0.2s; }
.game-card:nth-child(3) { animation-delay: 0.3s; }
.game-card:nth-child(4) { animation-delay: 0.4s; }
.game-card:nth-child(5) { animation-delay: 0.5s; }
.game-card:nth-child(6) { animation-delay: 0.6s; }
.game-card:nth-child(7) { animation-delay: 0.7s; }
.game-card:nth-child(8) { animation-delay: 0.8s; }
