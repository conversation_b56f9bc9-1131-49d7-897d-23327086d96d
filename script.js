// Game data for PG slots - <PERSON><PERSON><PERSON> tiếng Việt với icon emoji
const pgGames = [
    {
        id: 1,
        name: "🐅 <PERSON>ổ Vàng May Mắn",
        icon: "🐅",
        percentage: 87,
        currentValue: 87,
        maxValue: 100
    },
    {
        id: 2,
        name: "⚡ C<PERSON>ng Thiên <PERSON>ờng",
        icon: "⚡",
        percentage: 91,
        currentValue: 91,
        maxValue: 100
    },
    {
        id: 3,
        name: "🍭 Kẹo Ngọt Bonanza",
        icon: "🍭",
        percentage: 84,
        currentValue: 84,
        maxValue: 100
    },
    {
        id: 4,
        name: "⭐ <PERSON>ông <PERSON>",
        icon: "⭐",
        percentage: 79,
        currentValue: 79,
        maxValue: 100
    },
    {
        id: 5,
        name: "🤠 <PERSON>",
        icon: "🤠",
        percentage: 73,
        currentValue: 73,
        maxValue: 100
    },
    {
        id: 6,
        name: "🀄 <PERSON><PERSON><PERSON><PERSON>ần Tà<PERSON>",
        icon: "🀄",
        percentage: 89,
        currentValue: 89,
        maxValue: 100
    },
    {
        id: 7,
        name: "🐱 <PERSON><PERSON><PERSON> T<PERSON>n <PERSON>à<PERSON>",
        icon: "🐱",
        percentage: 82,
        currentValue: 82,
        maxValue: 100
    },
    {
        id: 8,
        name: "🐉 Rồng Nở Trứng",
        icon: "🐉",
        percentage: 86,
        currentValue: 86,
        maxValue: 100
    },
    {
        id: 9,
        name: "🍬 Tiệc Kẹo Ngọt",
        icon: "🍬",
        percentage: 78,
        currentValue: 78,
        maxValue: 100
    },
    {
        id: 10,
        name: "💎 Ngọc Quý Aztec",
        icon: "💎",
        percentage: 75,
        currentValue: 75,
        maxValue: 100
    },
    {
        id: 11,
        name: "🏜️ Vàng Miền Tây",
        icon: "🏜️",
        percentage: 81,
        currentValue: 81,
        maxValue: 100
    },
    {
        id: 12,
        name: "🍓 Tiệc Trái Cây",
        icon: "🍓",
        percentage: 88,
        currentValue: 88,
        maxValue: 100
    },
    {
        id: 13,
        name: "🏺 Kho Báu Aztec",
        icon: "🏺",
        percentage: 77,
        currentValue: 77,
        maxValue: 100
    },
    {
        id: 14,
        name: "🌸 Mỹ Nhân Kế",
        icon: "🌸",
        percentage: 83,
        currentValue: 83,
        maxValue: 100
    },
    {
        id: 15,
        name: "🐘 Thần Voi Vàng",
        icon: "🐘",
        percentage: 80,
        currentValue: 80,
        maxValue: 100
    },
    {
        id: 16,
        name: "🧛 Ma Cà Rồng",
        icon: "🧛",
        percentage: 74,
        currentValue: 74,
        maxValue: 100
    },
    {
        id: 17,
        name: "🌴 Rừng Nhiệt Đới",
        icon: "🌴",
        percentage: 76,
        currentValue: 76,
        maxValue: 100
    },
    {
        id: 18,
        name: "🦁 Sư Tử Thịnh Vượng",
        icon: "🦁",
        percentage: 85,
        currentValue: 85,
        maxValue: 100
    },
    {
        id: 19,
        name: "💰 Thần Tài Thắng Lớn",
        icon: "💰",
        percentage: 92,
        currentValue: 92,
        maxValue: 100
    },
    {
        id: 20,
        name: "🎰 Vận May Kép",
        icon: "🎰",
        percentage: 79,
        currentValue: 79,
        maxValue: 100
    }
];

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    loadGames();
    setupSearch();
    updatePercentages();
});

// Load games into grid
function loadGames() {
    const gamesGrid = document.getElementById('gamesGrid');
    gamesGrid.innerHTML = '';
    
    pgGames.forEach(game => {
        const gameCard = createGameCard(game);
        gamesGrid.appendChild(gameCard);
    });
}

// Create game card element
function createGameCard(game) {
    const card = document.createElement('div');
    card.className = 'game-card';
    card.onclick = () => selectGame(game);

    card.innerHTML = `
        <div class="game-header">
            <div class="game-icon-emoji">${game.icon}</div>
            <div class="game-info">
                <h3>${game.name}</h3>
            </div>
        </div>
        <div class="game-percentage">${game.percentage}%</div>
        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill" style="width: ${game.percentage}%"></div>
            </div>
            <div class="progress-text">
                <span>${game.currentValue}</span>
                <span>${game.maxValue}</span>
            </div>
        </div>
    `;

    return card;
}

// Select game for prediction
function selectGame(game) {
    localStorage.setItem('selectedGame', JSON.stringify(game));
    showNotification(`Đã chọn game: ${game.name}`);
}

// Setup search functionality
function setupSearch() {
    const searchInput = document.querySelector('.search-input');
    searchInput.addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        filterGames(searchTerm);
    });
}

// Filter games based on search
function filterGames(searchTerm) {
    const filteredGames = pgGames.filter(game => 
        game.name.toLowerCase().includes(searchTerm)
    );
    
    const gamesGrid = document.getElementById('gamesGrid');
    gamesGrid.innerHTML = '';
    
    filteredGames.forEach(game => {
        const gameCard = createGameCard(game);
        gamesGrid.appendChild(gameCard);
    });
}

// Update percentages with animation
function updatePercentages() {
    setInterval(() => {
        pgGames.forEach(game => {
            // Simulate percentage changes
            const change = Math.random() * 6 - 3; // -3 to +3
            game.percentage = Math.max(0, Math.min(100, game.percentage + change));
            game.currentValue = Math.round(game.percentage);
        });
        
        loadGames();
    }, 5000); // Update every 5 seconds
}

// Open prediction tool
function openPredictionTool() {
    window.open('prediction.html', '_blank');
}

// Show notification
function showNotification(message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(45deg, #00ff66, #66ff99);
        color: #000;
        padding: 15px 25px;
        border-radius: 10px;
        font-weight: 700;
        z-index: 1000;
        animation: slideIn 0.3s ease;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Add CSS animation for notification
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);
